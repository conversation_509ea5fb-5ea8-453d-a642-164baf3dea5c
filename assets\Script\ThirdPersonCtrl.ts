import { _decorator, CharacterController, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ThirdPersonCtrl')
export class ThirdPersonCtrl extends Component {

    _characterController: CharacterController;

    start() {
        this._characterController = this.node.getComponent(CharacterController)
        console.log("step offset ",this._characterController.stepOffset)
    }

    update(deltaTime: number) {
        
    }
}


